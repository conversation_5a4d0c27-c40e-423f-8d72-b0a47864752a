{% extends "base.html" %}

{% block title %}浏览器多账号绿色版 - 实例管理{% endblock %}

{% block content %}
<!-- 统计信息卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-browser fa-2x mb-2"></i>
                <div class="stats-number">{{ instances|length }}</div>
                <div>总实例数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-play-circle fa-2x mb-2"></i>
                <div class="stats-number" id="runningCount">0</div>
                <div>运行中</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <i class="fas fa-stop-circle fa-2x mb-2"></i>
                <div class="stats-number" id="stoppedCount">{{ instances|length }}</div>
                <div>已停止</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fab fa-chrome fa-2x mb-2"></i>
                <div class="stats-number">
                    <i class="fas fa-check" id="chromeStatus"></i>
                </div>
                <div>Chrome可用</div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tools me-2"></i>
                    快速操作
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus me-2"></i>新建实例
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshInstances()">
                        <i class="fas fa-sync-alt me-2"></i>刷新列表
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showBatchModal()">
                        <i class="fas fa-layer-group me-2"></i>批量配置
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="checkHealth()">
                        <i class="fas fa-heartbeat me-2"></i>系统检查
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实例列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    浏览器实例列表
                    <span class="badge bg-primary ms-2">{{ instances|length }}个</span>
                </h5>
            </div>
            <div class="card-body">
                {% if instances %}
                <div class="row" id="instancesList">
                    {% for instance in instances %}
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card instance-card h-100" data-instance="{{ instance.name }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">
                                        <i class="fab fa-chrome me-2 text-primary"></i>
                                        {{ instance.display_name or instance.name }}
                                    </h6>
                                    <span class="badge status-badge bg-secondary" id="status-{{ instance.name }}">
                                        已停止
                                    </span>
                                </div>
                                
                                {% if instance.description %}
                                <p class="card-text text-muted small mb-2">
                                    {{ instance.description }}
                                </p>
                                {% endif %}
                                
                                <div class="small text-muted mb-3">
                                    <div><i class="fas fa-home me-1"></i> {{ instance.homepage or 'https://www.google.com' }}</div>
                                    <div><i class="fas fa-calendar me-1"></i> {{ instance.created_date or '未知' }}</div>
                                </div>
                                
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-success btn-sm" 
                                            onclick="launchInstance('{{ instance.name }}')"
                                            id="launch-{{ instance.name }}">
                                        <i class="fas fa-play me-1"></i>启动
                                        <span class="loading spinner-border spinner-border-sm ms-1"></span>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                            onclick="editInstance('{{ instance.name }}')">
                                        <i class="fas fa-edit me-1"></i>编辑
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteInstance('{{ instance.name }}')">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-browser fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无浏览器实例</h4>
                    <p class="text-muted">点击"新建实例"创建您的第一个浏览器实例</p>
                    <button type="button" class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus me-2"></i>立即创建
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 新建实例模态框 -->
<div class="modal fade" id="createModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>新建浏览器实例
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createForm">
                    <div class="mb-3">
                        <label for="instanceName" class="form-label">实例名称 *</label>
                        <input type="text" class="form-control" id="instanceName" required
                               placeholder="例如：work, personal, shopping">
                        <div class="form-text">只能包含字母、数字、下划线和连字符</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="displayName" class="form-label">显示名称 *</label>
                        <input type="text" class="form-control" id="displayName" required
                               placeholder="例如：Chrome - 工作">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" rows="2"
                                  placeholder="简要描述这个实例的用途"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="homepage" class="form-label">主页URL</label>
                        <input type="url" class="form-control" id="homepage" 
                               value="https://www.google.com"
                               placeholder="https://www.google.com">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createInstance()">
                    <i class="fas fa-plus me-2"></i>创建实例
                    <span class="loading spinner-border spinner-border-sm ms-2"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量配置模态框 -->
<div class="modal fade" id="batchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-layer-group me-2"></i>批量配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">选择预定义的配置模板，一键创建多个浏览器实例。</p>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">基础实例集合</h6>
                                <p class="card-text small text-muted">工作、个人、购物三个基础实例</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="applyBatchConfig('basic_set')">
                                    应用配置
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">开发者实例集合</h6>
                                <p class="card-text small text-muted">专为开发者设计的实例集合</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="applyBatchConfig('developer_set')">
                                    应用配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 显示新建实例模态框
function showCreateModal() {
    const modal = new bootstrap.Modal(document.getElementById('createModal'));
    modal.show();
}

// 显示批量配置模态框
function showBatchModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchModal'));
    modal.show();
}

// 创建实例
async function createInstance() {
    const button = event.target;
    showLoading(button);
    
    try {
        const formData = {
            name: document.getElementById('instanceName').value.trim(),
            display_name: document.getElementById('displayName').value.trim(),
            description: document.getElementById('description').value.trim(),
            homepage: document.getElementById('homepage').value.trim()
        };
        
        const result = await apiRequest('/api/instances', {
            method: 'POST',
            body: JSON.stringify(formData)
        });
        
        showToast('实例创建成功！', 'success');
        bootstrap.Modal.getInstance(document.getElementById('createModal')).hide();
        
        // 重置表单
        document.getElementById('createForm').reset();
        document.getElementById('homepage').value = 'https://www.google.com';
        
        // 刷新页面
        setTimeout(() => location.reload(), 1000);
        
    } catch (error) {
        showToast(error.message, 'error');
    } finally {
        hideLoading(button);
    }
}

// 启动实例
async function launchInstance(name) {
    const button = document.getElementById(`launch-${name}`);
    showLoading(button);
    
    try {
        await apiRequest(`/api/instances/${name}/launch`, {
            method: 'POST'
        });
        
        showToast(`实例 ${name} 启动成功！`, 'success');
        
        // 更新状态显示
        const statusBadge = document.getElementById(`status-${name}`);
        statusBadge.textContent = '运行中';
        statusBadge.className = 'badge status-badge bg-success';
        
    } catch (error) {
        showToast(error.message, 'error');
    } finally {
        hideLoading(button);
    }
}

// 删除实例
async function deleteInstance(name) {
    if (!confirm(`确定要删除实例 "${name}" 吗？此操作不可恢复！`)) {
        return;
    }
    
    try {
        await apiRequest(`/api/instances/${name}`, {
            method: 'DELETE'
        });
        
        showToast(`实例 ${name} 删除成功！`, 'success');
        
        // 移除卡片
        const card = document.querySelector(`[data-instance="${name}"]`).closest('.col-lg-4');
        card.remove();
        
        // 更新统计
        updateStats();
        
    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 编辑实例
function editInstance(name) {
    showToast('编辑功能开发中...', 'info');
}

// 应用批量配置
function applyBatchConfig(template) {
    showToast(`批量配置 ${template} 功能开发中...`, 'info');
}

// 检查系统健康状态
async function checkHealth() {
    try {
        const result = await apiRequest('/health');
        const status = result.chrome_portable ? '正常' : '异常';
        const type = result.chrome_portable ? 'success' : 'warning';
        showToast(`系统状态: ${status}`, type);
    } catch (error) {
        showToast('健康检查失败', 'error');
    }
}

// 更新统计信息
function updateStats() {
    const totalInstances = document.querySelectorAll('.instance-card').length;
    const runningInstances = document.querySelectorAll('.bg-success').length;
    
    document.querySelector('.stats-number').textContent = totalInstances;
    document.getElementById('runningCount').textContent = runningInstances;
    document.getElementById('stoppedCount').textContent = totalInstances - runningInstances;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查Chrome状态
    checkHealth();
    
    // 更新统计
    updateStats();
});
</script>
{% endblock %}
