<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}浏览器多账号绿色版 v0.002{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .instance-card {
            cursor: pointer;
            transition: transform 0.2s ease-in-out;
        }
        
        .instance-card:hover {
            transform: translateY(-2px);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .btn-action {
            margin: 0.125rem;
        }
        
        .footer {
            background-color: #343a40;
            color: white;
            padding: 1rem 0;
            margin-top: 2rem;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1050;
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
        }
        
        .form-label {
            font-weight: 600;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stats-card .card-body {
            padding: 1.5rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .chrome-logo {
            width: 24px;
            height: 24px;
            margin-right: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container-fluid {
                padding: 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fab fa-chrome chrome-logo"></i>
                浏览器多账号绿色版
                <span class="badge bg-light text-primary ms-2">v0.002</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showCreateModal()">
                            <i class="fas fa-plus"></i> 新建实例
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/health">
                            <i class="fas fa-heartbeat"></i> 系统状态
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="refreshInstances()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://github.com" target="_blank">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Toast 通知容器 -->
    <div class="toast-container">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="footer mt-auto">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fab fa-chrome me-2"></i>
                        浏览器多账号绿色版 v0.002
                    </p>
                    <small class="text-muted">基于Chrome Portable的多账号隔离解决方案</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-2"></i>
                        由 AI Architecture System 构建
                    </p>
                    <small class="text-muted">架构驱动 · 智能化设计</small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = toast.querySelector('.toast-body');
            const toastHeader = toast.querySelector('.toast-header');
            const icon = toastHeader.querySelector('i');
            
            // 设置消息
            toastBody.textContent = message;
            
            // 设置图标和颜色
            icon.className = 'me-2';
            switch(type) {
                case 'success':
                    icon.classList.add('fas', 'fa-check-circle', 'text-success');
                    break;
                case 'error':
                    icon.classList.add('fas', 'fa-exclamation-circle', 'text-danger');
                    break;
                case 'warning':
                    icon.classList.add('fas', 'fa-exclamation-triangle', 'text-warning');
                    break;
                default:
                    icon.classList.add('fas', 'fa-info-circle', 'text-primary');
            }
            
            // 显示Toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // 显示加载状态
        function showLoading(element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.classList.add('show');
            }
            element.disabled = true;
        }
        
        // 隐藏加载状态
        function hideLoading(element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.classList.remove('show');
            }
            element.disabled = false;
        }
        
        // API请求封装
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || '请求失败');
                }
                
                return data;
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }
        
        // 刷新实例列表
        function refreshInstances() {
            location.reload();
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
