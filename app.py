#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - Web统一入口
基于Chrome Portable的多账号隔离浏览器解决方案

Author: AI Architecture System
Date: 2025-01-29
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from flask import Flask, render_template, request, jsonify, redirect, url_for
from werkzeug.serving import run_simple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.browser_manager import BrowserManager
    from core.config_manager import ConfigManager
except ImportError:
    # 如果核心模块不存在，创建基础实现
    print("核心模块不存在，使用基础实现...")

app = Flask(__name__)
app.secret_key = 'browser-multi-account-v0002'

# 全局配置
CONFIG = {
    'chrome_portable_path': 'GoogleChromePortable/GoogleChromePortable.exe',
    'instances_dir': 'instances',
    'icons_dir': 'icons',
    'config_dir': 'config'
}

class SimpleBrowserManager:
    """简化的浏览器管理器实现"""
    
    def __init__(self):
        self.instances_dir = Path(CONFIG['instances_dir'])
        self.chrome_path = Path(CONFIG['chrome_portable_path'])
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        self.instances_dir.mkdir(exist_ok=True)
        Path(CONFIG['icons_dir']).mkdir(exist_ok=True)
        Path(CONFIG['config_dir']).mkdir(exist_ok=True)
    
    def list_instances(self):
        """列出所有浏览器实例"""
        instances = []
        if not self.instances_dir.exists():
            return instances
        
        for instance_dir in self.instances_dir.iterdir():
            if instance_dir.is_dir():
                config_file = instance_dir / 'config.json'
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        config['name'] = instance_dir.name
                        config['status'] = 'stopped'  # 简化状态检查
                        instances.append(config)
                    except Exception as e:
                        print(f"读取实例配置失败 {instance_dir.name}: {e}")
        
        return instances
    
    def create_instance(self, name, display_name, description="", homepage="https://www.google.com"):
        """创建新的浏览器实例"""
        instance_dir = self.instances_dir / name
        if instance_dir.exists():
            raise ValueError(f"实例 {name} 已存在")
        
        # 创建实例目录结构
        instance_dir.mkdir(parents=True)
        data_dir = instance_dir / 'Data' / 'profile'
        data_dir.mkdir(parents=True)
        
        # 创建配置文件
        config = {
            'name': name,
            'display_name': display_name,
            'description': description,
            'homepage': homepage,
            'icon': 'default',
            'created_date': '2025-01-29',
            'chrome_args': [
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-background-timer-throttling'
            ]
        }
        
        config_file = instance_dir / 'config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return config
    
    def launch_instance(self, name):
        """启动浏览器实例"""
        if not self.chrome_path.exists():
            raise FileNotFoundError("Chrome Portable 未找到")
        
        instance_dir = self.instances_dir / name
        if not instance_dir.exists():
            raise ValueError(f"实例 {name} 不存在")
        
        # 读取配置
        config_file = instance_dir / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 构建启动命令
        data_dir = instance_dir / 'Data' / 'profile'
        cmd = [
            str(self.chrome_path),
            f'--user-data-dir={data_dir.absolute()}',
            f'--app-name={config["display_name"]}',
            config['homepage']
        ] + config.get('chrome_args', [])
        
        # 启动浏览器
        subprocess.Popen(cmd, cwd=str(self.chrome_path.parent))
        return True
    
    def delete_instance(self, name):
        """删除浏览器实例"""
        instance_dir = self.instances_dir / name
        if not instance_dir.exists():
            raise ValueError(f"实例 {name} 不存在")
        
        import shutil
        shutil.rmtree(instance_dir)
        return True

# 初始化浏览器管理器
browser_manager = SimpleBrowserManager()

@app.route('/')
def index():
    """主页"""
    instances = browser_manager.list_instances()
    return render_template('index.html', instances=instances)

@app.route('/api/instances')
def api_instances():
    """获取所有实例"""
    try:
        instances = browser_manager.list_instances()
        return jsonify({'success': True, 'data': instances})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/instances', methods=['POST'])
def api_create_instance():
    """创建新实例"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        display_name = data.get('display_name', '').strip()
        description = data.get('description', '').strip()
        homepage = data.get('homepage', 'https://www.google.com').strip()
        
        if not name or not display_name:
            return jsonify({'success': False, 'error': '实例名称和显示名称不能为空'})
        
        # 验证名称格式
        if not name.replace('_', '').replace('-', '').isalnum():
            return jsonify({'success': False, 'error': '实例名称只能包含字母、数字、下划线和连字符'})
        
        config = browser_manager.create_instance(name, display_name, description, homepage)
        return jsonify({'success': True, 'data': config})
    
    except ValueError as e:
        return jsonify({'success': False, 'error': str(e)})
    except Exception as e:
        return jsonify({'success': False, 'error': f'创建实例失败: {str(e)}'})

@app.route('/api/instances/<name>/launch', methods=['POST'])
def api_launch_instance(name):
    """启动实例"""
    try:
        browser_manager.launch_instance(name)
        return jsonify({'success': True, 'message': f'实例 {name} 启动成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'启动失败: {str(e)}'})

@app.route('/api/instances/<name>', methods=['DELETE'])
def api_delete_instance(name):
    """删除实例"""
    try:
        browser_manager.delete_instance(name)
        return jsonify({'success': True, 'message': f'实例 {name} 删除成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

@app.route('/health')
def health_check():
    """健康检查"""
    chrome_exists = Path(CONFIG['chrome_portable_path']).exists()
    return jsonify({
        'status': 'healthy',
        'chrome_portable': chrome_exists,
        'instances_count': len(browser_manager.list_instances())
    })

def check_environment():
    """检查运行环境"""
    issues = []
    
    # 检查Chrome Portable
    if not Path(CONFIG['chrome_portable_path']).exists():
        issues.append("Chrome Portable 未找到，请确保 GoogleChromePortable 目录存在")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("需要Python 3.7或更高版本")
    
    return issues

if __name__ == '__main__':
    print("🚀 浏览器多账号绿色版 v0.002 启动中...")
    print("=" * 50)
    
    # 环境检查
    issues = check_environment()
    if issues:
        print("⚠️  环境检查发现问题:")
        for issue in issues:
            print(f"   - {issue}")
        print()
    
    print("📊 系统信息:")
    print(f"   - Python版本: {sys.version}")
    print(f"   - 项目目录: {project_root}")
    print(f"   - Chrome路径: {CONFIG['chrome_portable_path']}")
    print(f"   - 实例目录: {CONFIG['instances_dir']}")
    
    instances = browser_manager.list_instances()
    print(f"   - 现有实例: {len(instances)}个")
    
    print()
    print("🌐 Web界面地址: http://127.0.0.1:5000")
    print("📖 使用说明: 在浏览器中打开上述地址进行管理")
    print("=" * 50)
    
    try:
        # 启动Flask应用
        app.run(host='127.0.0.1', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
