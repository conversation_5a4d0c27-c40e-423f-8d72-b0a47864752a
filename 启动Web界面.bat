@echo off
chcp 65001 >nul
title 浏览器多账号绿色版 v0.002 - Web界面启动器

echo.
echo ========================================
echo   浏览器多账号绿色版 v0.002
echo   Web统一入口启动器
echo ========================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo.
    echo 请确保已安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
echo 🐍 Python环境检查...
python --version

:: 检查Chrome Portable
if not exist "GoogleChromePortable\GoogleChromePortable.exe" (
    echo.
    echo ⚠️  警告：未找到Chrome Portable
    echo    路径：GoogleChromePortable\GoogleChromePortable.exe
    echo    部分功能可能无法正常使用
    echo.
)

:: 检查必要的Python包
echo.
echo 📦 检查Python依赖包...
python -c "import flask" 2>nul
if errorlevel 1 (
    echo ❌ 缺少Flask包，正在安装...
    pip install flask
    if errorlevel 1 (
        echo ❌ Flask安装失败，请手动安装：pip install flask
        pause
        exit /b 1
    )
)

python -c "import psutil" 2>nul
if errorlevel 1 (
    echo ❌ 缺少psutil包，正在安装...
    pip install psutil
    if errorlevel 1 (
        echo ❌ psutil安装失败，请手动安装：pip install psutil
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成

:: 创建必要的目录
if not exist "instances" mkdir instances
if not exist "icons" mkdir icons
if not exist "config" mkdir config
if not exist "core" mkdir core

echo.
echo 🚀 启动Web应用...
echo.
echo 📊 访问地址：http://127.0.0.1:5000
echo 📖 使用说明：在浏览器中打开上述地址进行管理
echo 🛑 停止服务：按 Ctrl+C 或关闭此窗口
echo.
echo ========================================

:: 启动Flask应用
python app.py

echo.
echo 👋 应用已停止
pause
